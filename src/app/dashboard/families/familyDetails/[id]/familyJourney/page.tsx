import { useState } from 'react';
import { useMobileMenuControl } from '@/contexts/MobileMenuContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

import SidebarContent from '@/components/dashboard/families/familyJourney/sideBar.tsx';
import {
  ChevronLeft,
  ChevronRight,
  Upload,
  Check,
  FileText,
  Users,
  MapPin,
  Home,
  Download,
  CheckCircle,
  User,
  FileCheck,
  Video,
  CalendarPlus2Icon as CalendarIcon2,
  Shield,
  GraduationCap,
  Heart,
  DollarSign,
  Menu,
} from 'lucide-react';


interface StepStatus {
  [key: string]: boolean;
}

//interface FormData {
//  [key: string]: any
//}

const stages = [
  {
    id: 1,
    title: 'Intake & Confirmation',
    description: 'Welcome and initial setup',
    icon: User,
    steps: [
      {
        id: 'assignment-confirmation',
        title: 'Assignment Confirmation',
        description: 'Review and confirm assignment',
        type: 'agreement',
        icon: FileCheck,
      },
      {
        id: 'intake-meeting',
        title: 'Intake Meeting & Guidelines',
        description: 'Video call and guidelines review',
        type: 'schedule',
        icon: Video,
      },
    ],
  },
  {
    id: 2,
    title: 'Profile Setup',
    description: 'Family information & preferences',
    icon: Users,
    steps: [
      {
        id: 'dear-aupair-letter',
        title: 'Dear Au-Pair Letter',
        description: 'Write introduction letter',
        type: 'text',
        icon: Heart,
      },
      {
        id: 'family-pictures',
        title: 'Family Pictures',
        description: 'Upload family photos',
        type: 'upload',
        icon: FileText,
      },
      {
        id: 'weekly-schedule',
        title: 'Weekly Time Schedule',
        description: 'Create work schedule',
        type: 'schedule',
        icon: CalendarIcon2,
      },
    ],
  },
  {
    id: 3,
    title: 'Compliance Check',
    description: 'Legal & income verification',
    icon: Shield,
    steps: [
      {
        id: 'proof-address',
        title: 'Proof of Address',
        description: 'BRP extract upload',
        type: 'upload',
        icon: FileText,
      },
      {
        id: 'legal-residence',
        title: 'Legal Residence',
        description: 'Passport and BSN details',
        type: 'form',
        icon: FileCheck,
      },
      {
        id: 'income-review',
        title: 'Income Review',
        description: 'Employment and salary documentation',
        type: 'upload',
        icon: DollarSign,
      },
    ],
  },
  {
    id: 4,
    title: 'Matching Process',
    description: 'Find your perfect Au Pair',
    icon: Heart,
    steps: [
      {
        id: 'matching-start',
        title: 'Begin Matching',
        description: 'Start the matching process',
        type: 'display',
        icon: Heart,
      },
      {
        id: 'candidate-review',
        title: 'Candidate Review',
        description: 'Review potential matches',
        type: 'form',
        icon: Users,
      },
    ],
  },
  {
    id: 6,
    title: 'Pre-Arrival',
    description: 'Preparation for Au Pair arrival',
    icon: Home,
    steps: [
      {
        id: 'room-preparation',
        title: 'Room Preparation',
        description: 'Prepare Au Pair accommodation',
        type: 'checklist',
        icon: Home,
      },
      {
        id: 'orientation-materials',
        title: 'Orientation Materials',
        description: 'Download preparation guides',
        type: 'download',
        icon: FileText,
      },
    ],
  },
  {
    id: 7,
    title: 'Arrival',
    description: 'Au Pair arrival and initial setup',
    icon: MapPin,
    steps: [
      {
        id: 'airport-pickup',
        title: 'Airport Pickup',
        description: 'Confirm arrival pickup',
        type: 'confirm',
        icon: MapPin,
      },
      {
        id: 'first-week',
        title: 'First Week Setup',
        description: 'Initial orientation tasks',
        type: 'checklist',
        icon: CheckCircle,
      },
    ],
  },
  {
    id: 8,
    title: 'Program Period',
    description: 'During the Au Pair program',
    icon: Users,
    steps: [
      {
        id: 'monthly-checkins',
        title: 'Monthly Check-ins',
        description: 'Regular program evaluations',
        type: 'form',
        icon: Heart,
      },
      {
        id: 'support-resources',
        title: 'Support Resources',
        description: 'Access help and guidance',
        type: 'info',
        icon: GraduationCap,
      },
    ],
  },
  {
    id: 9,
    title: 'Program Completion',
    description: 'End of program procedures',
    icon: CheckCircle,
    steps: [
      {
        id: 'final-evaluation',
        title: 'Final Evaluation',
        description: 'Program completion assessment',
        type: 'form',
        icon: FileCheck,
      },
      {
        id: 'departure-checklist',
        title: 'Departure Checklist',
        description: 'Final departure tasks',
        type: 'checklist',
        icon: CheckCircle,
      },
    ],
  },
];

export default function ModernAupairForm() {
  const { activeMobileMenu, toggleMobileMenu, closeMobileMenu } = useMobileMenuControl();
  // State for current stage and step
  const [currentStage, setCurrentStage] = useState(1);
  const [currentStep, setCurrentStep] = useState(0);
  const [expandedStages, setExpandedStages] = useState([1]);
  // const [mobileMenuOpen, setMobileMenuOpen] = useState(false); // Replaced by context
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false); // For desktop collapsed state
  const [stepStatus, setStepStatus] = useState<StepStatus>({
    'assignment-confirmation': true,
    'intake-meeting': true,
    'dear-aupair-letter': true,
    'family-pictures': true,
  });
  //const [formData, setFormData] = useState<FormData>({})
  //const [date, setDate] = useState<Date>()

  const currentStageData = stages.find(stage => stage.id === currentStage)!;

  const currentStepData = currentStageData.steps[currentStep];

  const nextStep = () => {
    if (currentStep < currentStageData.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else if (currentStage < stages.length) {
      setCurrentStage(currentStage + 1);
      setCurrentStep(0);
      // Expand the next stage
      if (!expandedStages.includes(currentStage + 1)) {
        setExpandedStages([...expandedStages, currentStage + 1]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else if (currentStage > 1) {
      setCurrentStage(currentStage - 1);
      setCurrentStep(stages[currentStage - 2].steps.length - 1);
      // Expand the previous stage
      if (!expandedStages.includes(currentStage - 1)) {
        setExpandedStages([...expandedStages, currentStage - 1]);
      }
    }
  };

  // Handler to mark a step as complete and move to next step
  const markStepComplete = (stepId: string) => {
    setStepStatus(prev => ({ ...prev, [stepId]: true }));
    setTimeout(nextStep, 500);
  };

  // Handler to expand/collapse a stage
  const toggleStageExpansion = (stageId: number) => {
    if (expandedStages.includes(stageId)) {
      setExpandedStages(expandedStages.filter(id => id !== stageId));
    } else {
      setExpandedStages([...expandedStages, stageId]);
    }
  };

  // Handler to go to a specific step in a stage
  const goToStep = (stageId: number, stepIndex: number) => {
    setCurrentStage(stageId);
    setCurrentStep(stepIndex);
    closeMobileMenu(); // Close mobile menu when navigating
  };


  // Define a proper type for steps to avoid 'any'
  interface StepData {
    id: string;
    title: string;
    description: string;
    type: string;
    icon: React.ElementType;
  }
  
  // Render the content for the current step
  const renderStepContent = (step: StepData) => {
    switch (step.type) {
      case 'agreement':
        if (step.id === 'assignment-confirmation') {
          return (
            <div className="space-y-4 lg:space-y-6">
              <Card>
                <CardHeader className="pb-4 lg:pb-6">
                  <CardTitle className="text-lg lg:text-xl">Assignment Confirmation</CardTitle>
                  <CardDescription className="text-sm lg:text-base">
                    Please read the Assignment Confirmation including Data Processing Agreement (AVG), our general terms
                    and conditions, the IND brochure and HBN fees carefully.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 lg:space-y-6">
                  <div className="bg-muted/50 rounded-lg p-4 lg:p-6">
                    <h4 className="font-semibold mb-3 lg:mb-4 text-sm lg:text-base">Required Documents to Review:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3">
                      <Button variant="outline" size="sm" className="justify-start text-xs lg:text-sm h-8 lg:h-9">
                        <Download className="h-3 w-3 lg:h-4 lg:w-4 mr-2" />
                        IND Brochure (NL)
                      </Button>
                      <Button variant="outline" size="sm" className="justify-start text-xs lg:text-sm h-8 lg:h-9">
                        <Download className="h-3 w-3 lg:h-4 lg:w-4 mr-2" />
                        IND Brochure (ENG)
                      </Button>
                      <Button variant="outline" size="sm" className="justify-start text-xs lg:text-sm h-8 lg:h-9">
                        <Download className="h-3 w-3 lg:h-4 lg:w-4 mr-2" />
                        Terms & Conditions
                      </Button>
                    </div>
                  </div>

                  <div className="bg-black text-white p-4 lg:p-6 rounded-lg">
                    <h4 className="font-semibold mb-3 lg:mb-4 text-sm lg:text-base">
                      Assignment Confirmation Document
                    </h4>
                    <p className="text-xs lg:text-sm mb-3 lg:mb-4 text-gray-300">
                      <strong>NOTE:</strong> Pop-up may be blocked, please allow pop-ups for this website.
                    </p>
                    <Button className="w-full bg-white hover:bg-stone-600 h-8 lg:h-10 text-xs lg:text-sm text-black">
                      <Download className="h-3 w-3 lg:h-4 lg:w-4 mr-2" />
                      DOWNLOAD ASSIGNMENT
                    </Button>
                  </div>

                  <div className="flex items-center space-x-2 p-3 lg:p-4 bg-muted/50 rounded-lg">
                    <Checkbox id="accept-assignment" />
                    <Label htmlFor="accept-assignment" className="text-xs lg:text-sm">
                      I have read and accept the assignment confirmation and all related documents
                    </Label>
                  </div>

                  <Button className="w-full h-9 lg:h-10 text-sm lg:text-base" onClick={() => markStepComplete(step.id)}>
                    Confirm Assignment
                  </Button>
                </CardContent>
              </Card>
            </div>
          );
        }
        return (
          <div className="space-y-4 lg:space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Terms & Conditions</CardTitle>
                <CardDescription>Please review and accept the program terms</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ScrollArea className="h-32 lg:h-48 w-full border rounded-md p-4">
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    By approving this agreement, you agree to our HBN Terms & Conditions and commit to participating in
                    the HBN Au Pair program. You also confirm that you are fully aware of your responsibilities, as well
                    as the legal obligations and conditions of the program.
                  </p>
                </ScrollArea>
                <div className="flex items-center space-x-2">
                  <Checkbox id="accept-terms" />
                  <Label htmlFor="accept-terms">I accept the terms and conditions</Label>
                </div>
                <Button className="w-full" onClick={() => markStepComplete(step.id)}>
                  Sign Agreement
                </Button>
              </CardContent>
            </Card>
          </div>
        );

      case 'text':
        return (
          <div className="space-y-4 lg:space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Dear Au-Pair Letter</CardTitle>
                <CardDescription>
                  Write a heartfelt introduction to your future Au Pair. This personal letter helps candidates
                  understand your family and what makes you special.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 lg:space-y-6">
                <div className="bg-muted/50 rounded-lg p-4 lg:p-6">
                  <h4 className="font-semibold mb-3 text-sm lg:text-base">Letter Suggestions:</h4>
                  <ul className="list-disc list-inside space-y-1 text-xs lg:text-sm text-muted-foreground">
                    <li>Short introduction of your family, parents, and children's personalities</li>
                    <li>Description of your home, garden, surroundings, and Au Pair room</li>
                    <li>Why you would like to welcome an Au Pair into your family</li>
                    <li>What you expect from your future Au Pair</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aupair-letter">Your Letter to Future Au Pairs</Label>
                  <Textarea
                    id="aupair-letter"
                    placeholder="Dear Au Pair,&#10;&#10;We are the [Family Name] family and we're excited to welcome you into our home! We live in..."
                    className="min-h-[150px] lg:min-h-[200px]"
                    defaultValue="Dear Au Pair,

We are the Johnson family and we're excited to welcome you into our home! We live in a beautiful suburban area with a large garden where our two children, Emma (6) and Lucas (4), love to play.

We're looking forward to sharing our culture with you while learning about yours. We hope this will be an amazing experience for all of us!

Best regards,
The Johnson Family"
                  />
                </div>
                <Button className="w-full" onClick={() => markStepComplete(step.id)}>
                  Save Letter
                </Button>
              </CardContent>
            </Card>
          </div>
        );

      case 'upload':
        if (step.id === 'family-pictures') {
          return (
            <div className="space-y-4 lg:space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Family Pictures</CardTitle>
                  <CardDescription>
                    Upload pictures of both parents and children. These photos help Au Pairs get to know your family
                    before they arrive.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 lg:space-y-6">
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-semibold mb-2 text-sm lg:text-base">Photo Guidelines:</h4>
                    <ul className="list-disc list-inside space-y-1 text-xs lg:text-sm text-muted-foreground">
                      <li>Upload only JPEG format images</li>
                      <li>Maximum 4 files allowed</li>
                      <li>Include photos of parents and children</li>
                      <li>Consider using collage tools like Piccolage for multiple photos</li>
                    </ul>
                  </div>

                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 lg:p-8 text-center hover:border-muted-foreground/50 transition-colors">
                    <Upload className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-muted-foreground mb-3 lg:mb-4" />
                    <h3 className="text-base lg:text-lg font-semibold mb-2">Upload Family Photos</h3>
                    <p className="text-muted-foreground mb-3 lg:mb-4 text-sm lg:text-base">
                      Drag and drop your photos here or click to browse
                    </p>
                    <Button variant="outline" size="sm" className="h-8 lg:h-9">
                      <Upload className="mr-2 h-3 w-3 lg:h-4 lg:w-4" />
                      Choose Photos
                    </Button>
                    <p className="text-xs text-muted-foreground mt-3 lg:mt-4">
                      Supported: JPEG only (Max 10MB per file)
                    </p>
                  </div>

                  <Button className="w-full" onClick={() => markStepComplete(step.id)}>
                    Continue
                  </Button>
                </CardContent>
              </Card>
            </div>
          );
        }

        if (step.id === 'income-review') {
          return (
            <div className="space-y-4 lg:space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Income Documentation</CardTitle>
                  <CardDescription>
                    Upload required employment and income verification documents as per Dutch legal requirements.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 lg:space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <DollarSign className="h-4 w-4 lg:h-5 lg:w-5 text-yellow-600" />
                      <h4 className="font-semibold text-yellow-800 text-sm lg:text-base">Income Requirements</h4>
                    </div>
                    <p className="text-xs lg:text-sm text-yellow-700">
                      Host families must have sufficient, independent, and sustainable income above the legal threshold.
                      <Button variant="link" className="p-0 h-auto text-yellow-800 underline ml-1 text-xs lg:text-sm">
                        Check current requirements
                      </Button>
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div className="bg-black text-white p-4 lg:p-6 rounded-lg text-center">
                      <FileText className="h-6 w-6 lg:h-8 lg:w-8 mx-auto mb-3" />
                      <h4 className="font-semibold mb-3 text-sm lg:text-base">Employer's Declaration</h4>
                      <Button
                        variant="secondary"
                        size="sm"
                        className="mb-3 bg-white text-black hover:bg-gray-100 h-7 lg:h-8 text-xs"
                      >
                        <Download className="h-3 w-3 lg:h-4 lg:w-4 mr-2" />
                        Download Form
                      </Button>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-3 lg:p-4">
                        <Upload className="h-4 w-4 lg:h-6 lg:w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload completed form</p>
                      </div>
                    </div>

                    <div className="bg-gray-900 text-white p-4 lg:p-6 rounded-lg text-center">
                      <FileText className="h-6 w-6 lg:h-8 lg:w-8 mx-auto mb-3" />
                      <h4 className="font-semibold mb-3 text-sm lg:text-base">Employment Agreement</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-4 lg:p-6 mb-3">
                        <Upload className="h-4 w-4 lg:h-6 lg:w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload contract</p>
                      </div>
                    </div>

                    <div className="bg-gray-700 text-white p-4 lg:p-6 rounded-lg text-center">
                      <FileText className="h-6 w-6 lg:h-8 lg:w-8 mx-auto mb-3" />
                      <h4 className="font-semibold mb-3 text-sm lg:text-base">Recent Salary Slips</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-4 lg:p-6 mb-3">
                        <Upload className="h-4 w-4 lg:h-6 lg:w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload 3 recent slips</p>
                      </div>
                    </div>
                  </div>

                  <Button className="w-full" onClick={() => markStepComplete(step.id)}>
                    Submit Documentation
                  </Button>
                </CardContent>
              </Card>
            </div>
          );
        }

        return (
          <div className="space-y-4 lg:space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Upload {step.title}</CardTitle>
                <CardDescription>Upload the required documents for this step</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 lg:p-8 text-center hover:border-muted-foreground/50 transition-colors">
                  <Upload className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-muted-foreground mb-3 lg:mb-4" />
                  <h3 className="text-base lg:text-lg font-semibold mb-2">Upload Files</h3>
                  <p className="text-muted-foreground mb-3 lg:mb-4 text-sm lg:text-base">
                    Drag and drop your files here or click to browse
                  </p>
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-3 w-3 lg:h-4 lg:w-4" />
                    Choose Files
                  </Button>
                  <p className="text-xs text-muted-foreground mt-3 lg:mt-4">
                    Supported formats: PDF, JPG, PNG (Max 10MB)
                  </p>
                </div>
                <Button className="w-full mt-4" onClick={() => markStepComplete(step.id)}>
                  Continue
                </Button>
              </CardContent>
            </Card>
          </div>
        );

      // Add other cases with similar responsive improvements...
      default:
        return (
          <div className="space-y-4 lg:space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{step.title}</CardTitle>
                <CardDescription>{step.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Content for {step.title} will be implemented here.</p>
                <Button className="w-full" onClick={() => markStepComplete(step.id)}>
                  Continue
                </Button>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      <div className="flex flex-1 overflow-hidden">
        {/* Main Content */}
        <div className="flex-1 flex flex-col min-w-0 overflow-hidden transition-all duration-300">
          {/* Header - Current Stage */}
          <div className="border-b p-3 lg:p-4 xl:p-6 bg-muted/5 flex-shrink-0">
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                {/* Mobile Menu Button */}
                <Sheet open={activeMobileMenu === 'familyJourney'} onOpenChange={(isOpen) => { if (isOpen) { toggleMobileMenu('familyJourney'); } else { closeMobileMenu(); } }}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="lg:hidden flex-shrink-0 h-8 w-8 p-0" onClick={() => toggleMobileMenu('familyJourney')}>
                      <Menu className="h-4 w-4" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-72 p-0 px-0 py-0">
                    <SidebarContent
                      stages={stages}
                      currentStage={currentStage}
                      currentStep={currentStep}
                      expandedStages={expandedStages}
                      stepStatus={stepStatus}
                      toggleStageExpansion={toggleStageExpansion}
                      goToStep={goToStep}
                      isMobileMenuOpen={activeMobileMenu === 'familyJourney'}
                      closeMobileMenu={closeMobileMenu} // Changed from onCollapsedChange
                    />
                  </SheetContent>
                </Sheet>
                </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="p-3 lg:p-4 xl:p-6 pb-16 lg:pb-6">{renderStepContent(currentStepData)}</div>
            </ScrollArea>
          </div>

          {/* Navigation Footer */}
          <div className="border-t p-3 lg:p-4 xl:p-6 flex-shrink-0 bg-background">
            <div className="flex items-center justify-between gap-2">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStage === 1 && currentStep === 0}
                className="h-8 lg:h-9 px-3 lg:px-4"
                size="sm"
              >
                <ChevronLeft className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                <span className="text-xs lg:text-sm">Previous</span>
              </Button>

              <div className="text-center min-w-0 flex-1 mx-2">
                <div className="text-xs lg:text-sm text-muted-foreground truncate">
                  Step {currentStep + 1} of {currentStageData.steps.length}
                </div>
                <div className="font-medium text-xs lg:text-sm truncate">{currentStageData.title}</div>
              </div>

              <Button
                onClick={() => markStepComplete(currentStepData.id)}
                disabled={stepStatus[currentStepData.id]}
                className="h-8 lg:h-9 px-3 lg:px-4"
                size="sm"
              >
                {stepStatus[currentStepData.id] ? (
                  <>
                    <span className="text-xs lg:text-sm">Done</span>
                    <Check className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
                  </>
                ) : (
                  <>
                    <span className="text-xs lg:text-sm">Continue</span>
                    <ChevronRight className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Sidebar - Right Side */}
        <div className={cn(
          "hidden lg:flex border-l bg-muted/10 flex-shrink-0 transition-all duration-300",
          sidebarCollapsed ? "w-16" : "w-64 xl:w-72"
        )}>
          <SidebarContent
            stages={stages}
            currentStage={currentStage}
            currentStep={currentStep}
            expandedStages={expandedStages}
            stepStatus={stepStatus}
            toggleStageExpansion={toggleStageExpansion}
            goToStep={goToStep}
            initialCollapsed={sidebarCollapsed}
            onCollapsedChange={setSidebarCollapsed}
          />
        </div>
      </div>

    </div>
  );
}
